use std::{collections::HashMap, sync::RwLock};

use alloy::primitives::{Address, U256};
use serde::{Deserialize, Serialize};

#[derive(Debug, Default, Serialize, Deserialize)]
pub struct User {
    pub balances : RwLock<HashMap<Address, U256>>,
}

impl User {
    pub fn new() -> Self {
        User {
            ..Default::default()
        }
    }
}

impl User {
    pub fn split_amounts(balances : HashMap<Address, U256>, split: Option<usize>) -> (Vec<Address>, Vec<U256>) {
        let split = split.unwrap_or(1);
        // 把balances的keys和values分别生成两个新的vec!
        let stable_addrs: Vec<_> = balances.keys().cloned().collect();
        let stable_amounts: Vec<_> = balances.values().cloned().map(|v| v / U256::from(split)).collect();

        if stable_amounts.iter().any(|v| *v == U256::ZERO) {
            panic!("balances has zero value");
        }

        if stable_addrs.len() == 0 {
            panic!("stable_addrs is empty");
        }

        (stable_addrs, stable_amounts)
    }

}