use std::collections::HashMap;
use std::str::FromStr;
use std::sync::atomic::Ordering;
use std::sync::atomic::AtomicUsize;
use alloy::primitives::Address;

use crate::CONFIG;

#[derive(Debug)]
pub struct NonceManager {
    pub nonces : HashMap<Address, AtomicUsize>,
}

impl NonceManager {
    /// 需要在CONFIG初始化后才能执行，把CONFIG的所有钱包加入到数据中
    pub fn new() -> Self {
        let mut nonces = HashMap::new();
        for operator_group in &CONFIG.operators {
            for (address_str, _) in operator_group {
                let address = Address::from_str(address_str).expect("Invalid operator address");
                nonces.insert(address, AtomicUsize::new(0));
            }
        }
        Self {
            nonces : nonces,
        }
    }   


    pub fn get_nonce(&self, address: &Address) -> usize {
         self.nonces
            .get(address)
            .map(|nonce| nonce.load(Ordering::Relaxed))
            .unwrap_or(0)
    }

    pub fn increment_nonce(&self, address: &Address) -> usize {
        if let Some(nonce) = self.nonces.get(address) {
            nonce.fetch_add(1, Ordering::Relaxed)
        } else {
            0
        }
    }

    pub fn set_nonce(&self, address: &Address, nonce: usize) {
        if let Some(n) = self.nonces.get(address) {
            n.store(nonce, Ordering::Relaxed);
        }
    }
}